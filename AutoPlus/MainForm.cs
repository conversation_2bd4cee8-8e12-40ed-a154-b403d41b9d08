using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using System.Runtime.InteropServices;

namespace AutoPlus
{
    public partial class MainForm : Form
    {
        private bool isDragging = false;
        private Point lastCursor;
        private Point lastForm;

        // Views
        private AccountsView accountsView;
        private ControlView controlView;
        private AboutView aboutView;
        private UserControl currentView;

        // Sidebar state
        private bool sidebarCollapsed = false;
        private int sidebarExpandedWidth = 150;
        private int sidebarCollapsedWidth = 45;

        public MainForm()
        {
            InitializeComponent();

            // Đảm bảo form có thể resize
            this.FormBorderStyle = FormBorderStyle.None;
            this.MaximizeBox = true;
            this.MinimizeBox = true;

            // Thêm event handler cho resize
            this.Resize += MainForm_Resize;

            // Khởi tạo views
            InitializeViews();

            // Load view mặc định (Accounts)
            LoadView(accountsView);
            SetActiveButton(btnAccounts);
        }

        private void InitializeViews()
        {
            accountsView = new AccountsView();
            controlView = new ControlView();
            aboutView = new AboutView();
        }

        private void LoadView(UserControl view)
        {
            // Xóa view hiện tại
            if (currentView != null)
            {
                contentPanel.Controls.Remove(currentView);
            }

            // Thêm view mới
            currentView = view;
            view.Dock = DockStyle.Fill;
            contentPanel.Controls.Add(view);
        }

        private void SetActiveButton(Button activeButton)
        {
            // Reset tất cả button về màu mặc định
            btnAccounts.BackColor = Color.FromArgb(45, 45, 48);
            btnControl.BackColor = Color.FromArgb(45, 45, 48);
            btnAbout.BackColor = Color.FromArgb(45, 45, 48);

            // Highlight button được chọn
            activeButton.BackColor = Color.FromArgb(0, 122, 204);
        }

        private void MainForm_Resize(object sender, EventArgs e)
        {
            // Cập nhật vị trí các nút khi form resize
            if (panel1 != null)
            {
                int panelWidth = panel1.Width;

                // Đặt lại vị trí các nút từ phải sang trái
                if (button1 != null) // Close button
                    button1.Location = new Point(panelWidth - 30, 0);

                if (button2 != null) // Maximize button
                    button2.Location = new Point(panelWidth - 60, 0);

                if (button3 != null) // Minimize button
                    button3.Location = new Point(panelWidth - 90, 0);
            }
        }

        // Nút Close (X)
        private void button1_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        // Nút Maximize/Restore (🗖)
        private void button2_Click(object sender, EventArgs e)
        {
            if (this.WindowState == FormWindowState.Maximized)
            {
                this.WindowState = FormWindowState.Normal;
                button2.Text = "🗖"; // Maximize icon
            }
            else
            {
                // Đặt vị trí form trước khi maximize để tránh lỗi hiển thị
                if (this.WindowState == FormWindowState.Minimized)
                {
                    this.WindowState = FormWindowState.Normal;
                }
                this.WindowState = FormWindowState.Maximized;
                button2.Text = "🗗"; // Restore icon
            }
        }

        // Nút Minimize (_)
        private void button3_Click(object sender, EventArgs e)
        {
            this.WindowState = FormWindowState.Minimized;
        }

        // Chức năng kéo thả form
        private void panel1_MouseDown(object sender, MouseEventArgs e)
        {
            if (e.Button == MouseButtons.Left)
            {
                isDragging = true;
                lastCursor = Cursor.Position;
                lastForm = this.Location;
            }
        }

        private void panel1_MouseMove(object sender, MouseEventArgs e)
        {
            if (isDragging)
            {
                Point currentCursor = Cursor.Position;
                Point offset = new Point(currentCursor.X - lastCursor.X, currentCursor.Y - lastCursor.Y);
                this.Location = new Point(lastForm.X + offset.X, lastForm.Y + offset.Y);
            }
        }

        private void panel1_MouseUp(object sender, MouseEventArgs e)
        {
            isDragging = false;
        }

        // Event handlers cho sidebar buttons
        private void btnAccounts_Click(object sender, EventArgs e)
        {
            LoadView(accountsView);
            SetActiveButton(btnAccounts);
        }

        private void btnControl_Click(object sender, EventArgs e)
        {
            LoadView(controlView);
            SetActiveButton(btnControl);
        }

        private void btnAbout_Click(object sender, EventArgs e)
        {
            LoadView(aboutView);
            SetActiveButton(btnAbout);
        }
    }
}