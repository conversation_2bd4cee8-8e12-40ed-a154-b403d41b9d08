using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using System.Runtime.InteropServices;

namespace AutoPlus
{
    public partial class MainForm : Form
    {
        private bool isDragging = false;
        private Point lastCursor;
        private Point lastForm;

        public MainForm()
        {
            InitializeComponent();

            // Đảm bảo form có thể resize
            this.FormBorderStyle = FormBorderStyle.None;
            this.MaximizeBox = true;
            this.MinimizeBox = true;

            // Thêm event handler cho resize
            this.Resize += MainForm_Resize;
        }

        private void MainForm_Resize(object sender, EventArgs e)
        {
            // Cập nhật vị trí các nút khi form resize
            if (panel1 != null)
            {
                int panelWidth = panel1.Width;

                // Đặt lại vị trí các nút từ ph<PERSON><PERSON> sang trái
                if (button1 != null) // Close button
                    button1.Location = new Point(panelWidth - 30, 0);

                if (button2 != null) // Maximize button
                    button2.Location = new Point(panelWidth - 60, 0);

                if (button3 != null) // Minimize button
                    button3.Location = new Point(panelWidth - 90, 0);
            }
        }

        // Nút Close (X)
        private void button1_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        // Nút Maximize/Restore (🗖)
        private void button2_Click(object sender, EventArgs e)
        {
            if (this.WindowState == FormWindowState.Maximized)
            {
                this.WindowState = FormWindowState.Normal;
                button2.Text = "🗖"; // Maximize icon
            }
            else
            {
                // Đặt vị trí form trước khi maximize để tránh lỗi hiển thị
                if (this.WindowState == FormWindowState.Minimized)
                {
                    this.WindowState = FormWindowState.Normal;
                }
                this.WindowState = FormWindowState.Maximized;
                button2.Text = "🗗"; // Restore icon
            }
        }

        // Nút Minimize (_)
        private void button3_Click(object sender, EventArgs e)
        {
            this.WindowState = FormWindowState.Minimized;
        }

        // Chức năng kéo thả form
        private void panel1_MouseDown(object sender, MouseEventArgs e)
        {
            if (e.Button == MouseButtons.Left)
            {
                isDragging = true;
                lastCursor = Cursor.Position;
                lastForm = this.Location;
            }
        }

        private void panel1_MouseMove(object sender, MouseEventArgs e)
        {
            if (isDragging)
            {
                Point currentCursor = Cursor.Position;
                Point offset = new Point(currentCursor.X - lastCursor.X, currentCursor.Y - lastCursor.Y);
                this.Location = new Point(lastForm.X + offset.X, lastForm.Y + offset.Y);
            }
        }

        private void panel1_MouseUp(object sender, MouseEventArgs e)
        {
            isDragging = false;
        }
    }
}