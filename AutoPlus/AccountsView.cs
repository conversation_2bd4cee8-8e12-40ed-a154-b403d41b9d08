using System;
using System.Drawing;
using System.Windows.Forms;

namespace AutoPlus
{
    public partial class AccountsView : UserControl
    {
        public AccountsView()
        {
            InitializeComponent();
            LoadSampleData();
        }

        private void LoadSampleData()
        {
            // Thêm dữ liệu mẫu vào DataGridView
            dataGridView1.Rows.Add("0", "s7ra1ndr0p311c", "V<PERSON> trụ 7", "Không có", "");
            dataGridView1.Rows.Add("1", "<EMAIL>", "<PERSON><PERSON> trụ 7", "Không có", "");
            dataGridView1.Rows.Add("2", "<EMAIL>", "<PERSON><PERSON> trụ 7", "Không có", "");
            dataGridView1.Rows.Add("3", "<EMAIL>", "<PERSON><PERSON> trụ 7", "Không có", "");
            dataGridView1.Rows.Add("4", "<EMAIL>", "<PERSON><PERSON> trụ 7", "<PERSON>h<PERSON><PERSON> có", "");
            dataGridView1.Rows.Add("5", "***********", "Vũ trụ 7", "Không có", "");
            dataGridView1.Rows.Add("6", "***********", "Vũ trụ 7", "Không có", "");
        }

        private void btnLogin_Click(object sender, EventArgs e)
        {
            MessageBox.Show("Chức năng đăng nhập đang được phát triển!", "Thông báo", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void btnLoginID_Click(object sender, EventArgs e)
        {
            MessageBox.Show("Chức năng đăng nhập ID đang được phát triển!", "Thông báo", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void btnAddAccount_Click(object sender, EventArgs e)
        {
            MessageBox.Show("Chức năng thêm tài khoản đang được phát triển!", "Thông báo", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void btnEditAccount_Click(object sender, EventArgs e)
        {
            MessageBox.Show("Chức năng sửa tài khoản đang được phát triển!", "Thông báo", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void btnDeleteAccount_Click(object sender, EventArgs e)
        {
            MessageBox.Show("Chức năng xóa tài khoản đang được phát triển!", "Thông báo", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void btnProfile1_Click(object sender, EventArgs e)
        {
            MessageBox.Show("Profile 1 được chọn!", "Thông báo", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void btnProfile2_Click(object sender, EventArgs e)
        {
            MessageBox.Show("Profile 2 được chọn!", "Thông báo", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void btnProfile3_Click(object sender, EventArgs e)
        {
            MessageBox.Show("Profile 3 được chọn!", "Thông báo", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }
    }
}
